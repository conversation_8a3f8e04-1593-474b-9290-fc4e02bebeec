<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">15%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 11:01 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html">app/config/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html">app/config/config.py</a></td>
                <td>134</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="105 134">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b___init___py.html">app/core_/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html">app/core_/client.py</a></td>
                <td>750</td>
                <td>565</td>
                <td>0</td>
                <td class="right" data-ratio="185 750">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html">app/core_/custom_ssh_client.py</a></td>
                <td>127</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html">app/core_/direct_ssh_streams.py</a></td>
                <td>131</td>
                <td>131</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html">app/core_/error_handler.py</a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html">app/core_/exceptions.py</a></td>
                <td>133</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="76 133">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html">app/core_/health.py</a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html">app/core_/kafka_service.py</a></td>
                <td>350</td>
                <td>350</td>
                <td>0</td>
                <td class="right" data-ratio="0 350">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html">app/core_/mcp_executor.py</a></td>
                <td>349</td>
                <td>349</td>
                <td>0</td>
                <td class="right" data-ratio="0 349">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html">app/core_/metrics.py</a></td>
                <td>113</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html">app/schemas/authentication_manager.py</a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html">app/schemas/client.py</a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html">app/services/authentication_manager.py</a></td>
                <td>115</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="32 115">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html">app/services/container_client.py</a></td>
                <td>156</td>
                <td>156</td>
                <td>0</td>
                <td class="right" data-ratio="0 156">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html">app/services/credential_service.py</a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html">app/services/ssh_manager.py</a></td>
                <td>299</td>
                <td>264</td>
                <td>0</td>
                <td class="right" data-ratio="35 299">12%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>3131</td>
                <td>2656</td>
                <td>0</td>
                <td class="right" data-ratio="475 3131">15%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 11:01 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_c318f3fa19a49f69_ssh_manager_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_5f5a17c013354698___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
