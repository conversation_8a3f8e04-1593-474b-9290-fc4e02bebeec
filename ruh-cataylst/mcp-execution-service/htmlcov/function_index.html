<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">15%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 11:01 +0530
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app/__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html">app/config/__init__.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t67">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t67"><data value='new__'>SettingsManager.__new__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t76">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t76"><data value='init__'>SettingsManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t81">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t81"><data value='load_settings'>SettingsManager._load_settings</data></a></td>
                <td>5</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="2 5">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t89">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t89"><data value='refresh'>SettingsManager.refresh</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t99">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t99"><data value='configure_logging'>SettingsManager._configure_logging</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t112">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t112"><data value='settings'>SettingsManager.settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t118">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t118"><data value='kafka_bootstrap_servers'>SettingsManager.kafka_bootstrap_servers</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t122">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t122"><data value='kafka_consumer_topic'>SettingsManager.kafka_consumer_topic</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t126">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t126"><data value='kafka_consumer_group_id'>SettingsManager.kafka_consumer_group_id</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t130">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t130"><data value='kafka_results_topic'>SettingsManager.kafka_results_topic</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t134">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t134"><data value='default_mcp_retries'>SettingsManager.default_mcp_retries</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t138">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t138"><data value='log_level'>SettingsManager.log_level</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t142">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t142"><data value='max_concurrent_tasks'>SettingsManager.max_concurrent_tasks</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t146">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t146"><data value='ssh_host'>SettingsManager.ssh_host</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t150">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t150"><data value='ssh_user'>SettingsManager.ssh_user</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t154">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t154"><data value='ssh_port'>SettingsManager.ssh_port</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t158">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t158"><data value='ssh_key_path'>SettingsManager.ssh_key_path</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t162">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t162"><data value='ssh_key_content'>SettingsManager.ssh_key_content</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t166">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t166"><data value='api_base_url'>SettingsManager.api_base_url</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t170">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t170"><data value='server_auth_key'>SettingsManager.server_auth_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t174">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t174"><data value='credential_cache_ttl'>SettingsManager.credential_cache_ttl</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t178">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t178"><data value='api_base_url'>SettingsManager.api_base_url</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t182">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t182"><data value='container_api_timeout'>SettingsManager.container_api_timeout</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t186">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t186"><data value='mcp_config_cache_ttl'>SettingsManager.mcp_config_cache_ttl</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t190">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t190"><data value='mcp_config_timeout'>SettingsManager.mcp_config_timeout</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t206">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t206"><data value='getattr__'>SettingsProxy.__getattr__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t209">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t209"><data value='model_dump'>SettingsProxy.model_dump</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t213">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t213"><data value='refresh'>SettingsProxy.refresh</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t234">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html#t234"><data value='refresh_settings'>refresh_settings</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html">app/config/config.py</a></td>
                <td class="name left"><a href="z_92724abef7b332ce_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>85</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="85 85">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b___init___py.html">app/core_/__init__.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t46">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t46"><data value='init__'>MCPClient.__init__</data></a></td>
                <td>69</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="52 69">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t194">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t194"><data value='build_ssh_command'>MCPClient._build_ssh_command</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t273">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t273"><data value='create_temp_key_file'>MCPClient._create_temp_key_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t287">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t287"><data value='check_container_exists'>MCPClient._check_container_exists</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t336">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t336"><data value='get_container_command'>MCPClient._get_container_command</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t468">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t468"><data value='establish_ssh_docker_connection'>MCPClient._establish_ssh_docker_connection</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t486">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t486"><data value='try_standard_stdio_connection'>MCPClient._try_standard_stdio_connection</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t527">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t527"><data value='try_fallback_ssh_connection'>MCPClient._try_fallback_ssh_connection</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t549">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t549"><data value='build_simple_ssh_command'>MCPClient._build_simple_ssh_command</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t581">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t581"><data value='establish_simple_stdio_connection'>MCPClient._establish_simple_stdio_connection</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t639">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t639"><data value='setup_ssh_host_verification'>MCPClient._setup_ssh_host_verification</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t661">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t661"><data value='build_ssh_docker_command'>MCPClient._build_ssh_docker_command</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t689">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t689"><data value='aenter__'>MCPClient.__aenter__</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t694">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t694"><data value='connect_with_retry'>MCPClient._connect_with_retry</data></a></td>
                <td>25</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="21 25">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t737">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t737"><data value='establish_connection'>MCPClient._establish_connection</data></a></td>
                <td>67</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="31 67">46%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t872">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t872"><data value='cleanup_partial_connection'>MCPClient._cleanup_partial_connection</data></a></td>
                <td>23</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="10 23">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t898">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t898"><data value='aexit__'>MCPClient.__aexit__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t904">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t904"><data value='close'>MCPClient.close</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t954">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t954"><data value='ensure_connected'>MCPClient._ensure_connected</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t982">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t982"><data value='perform_health_check'>MCPClient._perform_health_check</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1001">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1001"><data value='set_oauth_credentials'>MCPClient.set_oauth_credentials</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1016">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1016"><data value='set_api_key'>MCPClient.set_api_key</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1028">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1028"><data value='integrate_credential_service'>MCPClient.integrate_credential_service</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1107">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1107"><data value='list_resources'>MCPClient.list_resources</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1126">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1126"><data value='read_resource'>MCPClient.read_resource</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1140">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1140"><data value='list_tools'>MCPClient.list_tools</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1160">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1160"><data value='call_tool'>MCPClient.call_tool</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1190">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1190"><data value='fetch_tools'>MCPClient.fetch_tools</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1209">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1209"><data value='list_prompts'>MCPClient.list_prompts</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1229">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1229"><data value='get_prompt'>MCPClient.get_prompt</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1243">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1243"><data value='subscribe_resource'>MCPClient.subscribe_resource</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1255">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1255"><data value='unsubscribe_resource'>MCPClient.unsubscribe_resource</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1273">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1273"><data value='ping'>MCPClient.ping</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1284">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1284"><data value='get_connection_info'>MCPClient.get_connection_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1298">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1298"><data value='test_authentication'>MCPClient.test_authentication</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1315">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1315"><data value='create_authenticated_client'>create_authenticated_client</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1375">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1375"><data value='create_legacy_client'>create_legacy_client</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1391">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1391"><data value='create_client_with_credential_service'>create_client_with_credential_service</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1459">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html#t1459"><data value='main'>main</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html">app/core_/client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>93</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="70 93">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t20">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t20"><data value='init__'>CustomSSHMCPClient.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t37">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t37"><data value='connect'>CustomSSHMCPClient.connect</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t91">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t91"><data value='test_connection'>CustomSSHMCPClient._test_connection</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t126">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t126"><data value='read_response_with_timeout'>CustomSSHMCPClient._read_response_with_timeout</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t184">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t184"><data value='close'>CustomSSHMCPClient.close</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t203">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t203"><data value='init__'>CustomSSHReadStream.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t207">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t207"><data value='read'>CustomSSHReadStream.read</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t228">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t228"><data value='init__'>CustomSSHWriteStream.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t232">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t232"><data value='write'>CustomSSHWriteStream.write</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t245">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html#t245"><data value='flush'>CustomSSHWriteStream.flush</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html">app/core_/custom_ssh_client.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_custom_ssh_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t16">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t16"><data value='init__'>DirectSSHReadStream.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t21">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t21"><data value='read'>DirectSSHReadStream.read</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t63">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t63"><data value='aiter__'>DirectSSHReadStream.__aiter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t67">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t67"><data value='anext__'>DirectSSHReadStream.__anext__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t85">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t85"><data value='init__'>DirectSSHWriteStream.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t90">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t90"><data value='write'>DirectSSHWriteStream.write</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t107">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t107"><data value='send_request'>DirectSSHWriteStream.send_request</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t122">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t122"><data value='send_notification'>DirectSSHWriteStream.send_notification</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t137">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t137"><data value='init__'>DirectMCPSession.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t146">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t146"><data value='aenter__'>DirectMCPSession.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t150">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t150"><data value='aexit__'>DirectMCPSession.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t154">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t154"><data value='initialize'>DirectMCPSession.initialize</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t180">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t180"><data value='list_tools'>DirectMCPSession.list_tools</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t195">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t195"><data value='call_tool'>DirectMCPSession.call_tool</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t213">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t213"><data value='wait_for_response'>DirectMCPSession._wait_for_response</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t244">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html#t244"><data value='send_ping'>DirectMCPSession.send_ping</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html">app/core_/direct_ssh_streams.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_direct_ssh_streams_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t27">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t27"><data value='classify_exception'>classify_exception</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t98">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t98"><data value='is_retryable_error'>is_retryable_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t112">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t112"><data value='extract_error_context'>extract_error_context</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t155">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t155"><data value='format_kafka_error_response'>format_kafka_error_response</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t215">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t215"><data value='sanitize_context'>_sanitize_context</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t262">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t262"><data value='create_validation_error'>create_validation_error</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t285">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html#t285"><data value='create_infrastructure_error'>create_infrastructure_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html">app/core_/error_handler.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_error_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t65">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t65"><data value='init__'>MCPExecutorError.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t80">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t80"><data value='str__'>MCPExecutorError.__str__</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t85">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t85"><data value='to_kafka_error'>MCPExecutorError.to_kafka_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t95">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t95"><data value='sanitize_details'>MCPExecutorError._sanitize_details</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t122">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t122"><data value='init__'>MCPConfigNotFoundError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t137">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t137"><data value='init__'>MCPConfigInvalidError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t155">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t155"><data value='init__'>ContainerCreationError.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t178">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t178"><data value='init__'>ContainerExecutionError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t198">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t198"><data value='init__'>SSHConnectionError.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t221">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t221"><data value='init__'>MCPServerUnreachableError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t239">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t239"><data value='init__'>MCPAuthenticationError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t257">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t257"><data value='init__'>MCPToolExecutionError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t275">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t275"><data value='init__'>MCPHTTPMethodError.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t295">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t295"><data value='init__'>PayloadValidationError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t313">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t313"><data value='init__'>CredentialRetrievalError.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t349">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t349"><data value='get_exception_class'>get_exception_class</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t354">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html#t354"><data value='create_exception'>create_exception</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html">app/core_/exceptions.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>62</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="62 62">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t51">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t51"><data value='post_init__'>HealthCheckResult.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t66">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t66"><data value='post_init__'>SystemHealth.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t74">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t74"><data value='init__'>HealthChecker.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t80">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t80"><data value='check_kafka_connectivity'>HealthChecker.check_kafka_connectivity</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t151">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t151"><data value='check_container_api_connectivity'>HealthChecker.check_container_api_connectivity</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t211">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t211"><data value='check_mcp_config_service_connectivity'>HealthChecker.check_mcp_config_service_connectivity</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t271">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t271"><data value='check_service_health'>HealthChecker.check_service_health</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t304">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t304"><data value='get_system_health'>HealthChecker.get_system_health</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t346">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html#t346"><data value='get_health_status'>get_health_status</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html">app/core_/health.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t28">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t28"><data value='acquire'>InfiniteSemaphore.acquire</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t37">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t37"><data value='init__'>KafkaMCPService.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t52">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t52"><data value='load_config'>KafkaMCPService._load_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t65">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t65"><data value='init_kafka_components'>KafkaMCPService._init_kafka_components</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t98">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t98"><data value='setup_config_reload_signal'>KafkaMCPService._setup_config_reload_signal</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t113">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t113"><data value='handle_config_reload_signal'>KafkaMCPService._handle_config_reload_signal</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t128">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t128"><data value='reload_config'>KafkaMCPService.reload_config</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t166">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t166"><data value='start_consumer'>KafkaMCPService.start_consumer</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t332">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t332"><data value='send_service_status_update'>KafkaMCPService._send_service_status_update</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t371">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t371"><data value='stop_consumer'>KafkaMCPService.stop_consumer</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t436">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t436"><data value='send_error_response'>KafkaMCPService.send_error_response</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t477">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t477"><data value='send_error_response_from_exception'>KafkaMCPService.send_error_response_from_exception</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t501">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t501"><data value='process_message'>KafkaMCPService.process_message</data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t662">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t662"><data value='parse_result'>KafkaMCPService.process_message.parse_result</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t831">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t831"><data value='commit_offset'>KafkaMCPService._commit_offset</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t873">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html#t873"><data value='send_result'>KafkaMCPService.send_result</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html">app/core_/kafka_service.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_kafka_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t51">app/core_/mcp_executor.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t51"><data value='init__'>MCPExecutor.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t64">app/core_/mcp_executor.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t64"><data value='fetch_mcp_config'>MCPExecutor.fetch_mcp_config</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t161">app/core_/mcp_executor.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t161"><data value='execute_url_tool'>MCPExecutor._execute_url_tool</data></a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t673">app/core_/mcp_executor.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t673"><data value='execute_container_tool'>MCPExecutor._execute_container_tool</data></a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t981">app/core_/mcp_executor.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html#t981"><data value='execute_tool'>MCPExecutor.execute_tool</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html">app/core_/mcp_executor.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_mcp_executor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t54">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t54"><data value='post_init__'>ExecutionMetric.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t70">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t70"><data value='post_init__'>ConfigMetric.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t87">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t87"><data value='post_init__'>AuthenticationMetric.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t95">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t95"><data value='init__'>MetricsLogger.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t108">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t108"><data value='log_metric'>MetricsLogger._log_metric</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t113">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t113"><data value='log_execution_start'>MetricsLogger.log_execution_start</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t130">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t130"><data value='log_execution_success'>MetricsLogger.log_execution_success</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t148">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t148"><data value='log_execution_failure'>MetricsLogger.log_execution_failure</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t167">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t167"><data value='log_execution_retry'>MetricsLogger.log_execution_retry</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t182">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t182"><data value='log_config_fetch'>MetricsLogger.log_config_fetch</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t194">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t194"><data value='log_config_cache_hit'>MetricsLogger.log_config_cache_hit</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t204">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t204"><data value='log_config_cache_miss'>MetricsLogger.log_config_cache_miss</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t214">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t214"><data value='log_authentication_success'>MetricsLogger.log_authentication_success</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t227">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t227"><data value='log_authentication_failure'>MetricsLogger.log_authentication_failure</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t241">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t241"><data value='log_container_lifecycle'>MetricsLogger.log_container_lifecycle</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t261">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t261"><data value='log_execution_start'>log_execution_start</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t267">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t267"><data value='log_execution_success'>log_execution_success</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t275">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html#t275"><data value='log_execution_failure'>log_execution_failure</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html">app/core_/metrics.py</a></td>
                <td class="name left"><a href="z_fc1582fcccf5ee0b_metrics_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t15">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t15"><data value='main'>main</data></a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t97">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t97"><data value='signal_handler'>main.signal_handler</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html">app/schemas/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_authentication_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html">app/schemas/client.py</a></td>
                <td class="name left"><a href="z_c0f67d75e686303c_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html">app/services/__init__.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t15">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t15"><data value='init__'>AuthenticationManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t20">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t20"><data value='add_authentication'>AuthenticationManager.add_authentication</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t31">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t31"><data value='add_bearer_token'>AuthenticationManager.add_bearer_token</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t47">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t47"><data value='add_api_key'>AuthenticationManager.add_api_key</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t75">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t75"><data value='add_custom_auth'>AuthenticationManager.add_custom_auth</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t91">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t91"><data value='get_headers'>AuthenticationManager.get_headers</data></a></td>
                <td>28</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="4 28">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t153">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t153"><data value='get_query_params'>AuthenticationManager.get_query_params</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t169">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t169"><data value='get_body_params'>AuthenticationManager.get_body_params</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t185">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t185"><data value='get_config'>AuthenticationManager._get_config</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t193">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t193"><data value='get_valid_token'>AuthenticationManager._get_valid_token</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t216">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t216"><data value='validate_token'>AuthenticationManager.validate_token</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t227">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t227"><data value='clear_authentication'>AuthenticationManager.clear_authentication</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t239">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t239"><data value='get_headers_with_exceptions'>AuthenticationManager.get_headers_with_exceptions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t256">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html#t256"><data value='validate_token_with_exceptions'>AuthenticationManager.validate_token_with_exceptions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html">app/services/authentication_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_authentication_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t23">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t23"><data value='init__'>ContainerManagementClient.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t32">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t32"><data value='get_headers'>ContainerManagementClient._get_headers</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t48">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t48"><data value='create_container'>ContainerManagementClient.create_container</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t161">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t161"><data value='stop_container'>ContainerManagementClient.stop_container</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t246">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t246"><data value='create_container_with_exceptions'>ContainerManagementClient.create_container_with_exceptions</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t281">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t281"><data value='stop_container_with_exceptions'>ContainerManagementClient.stop_container_with_exceptions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t298">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t298"><data value='get_container_status'>ContainerManagementClient.get_container_status</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t352">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html#t352"><data value='delete_container'>ContainerManagementClient.delete_container</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html">app/services/container_client.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_container_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t17">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t17"><data value='init__'>CredentialService.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t31">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t31"><data value='is_cache_valid'>CredentialService._is_cache_valid</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t39">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t39"><data value='get_from_cache'>CredentialService._get_from_cache</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t52">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t52"><data value='store_in_cache'>CredentialService._store_in_cache</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t58">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t58"><data value='get_oauth_credentials'>CredentialService.get_oauth_credentials</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t165">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t165"><data value='clear_cache'>CredentialService.clear_cache</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t183">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html#t183"><data value='get_cache_stats'>CredentialService.get_cache_stats</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html">app/services/credential_service.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_credential_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t23">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t23"><data value='init__'>SecureSSHKeyManager.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t26">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t26"><data value='create_temp_key_file'>SecureSSHKeyManager._create_temp_key_file</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t54">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t54"><data value='cleanup_temp_key_file'>SecureSSHKeyManager.cleanup_temp_key_file</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t74">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t74"><data value='get_ssh_key_path'>SecureSSHKeyManager.get_ssh_key_path</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t137">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t137"><data value='get_ssh_key_path_with_exceptions'>SecureSSHKeyManager.get_ssh_key_path_with_exceptions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t152">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t152"><data value='del__'>SecureSSHKeyManager.__del__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t166">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t166"><data value='new__'>GlobalSSHKeyManager.__new__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t171">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t171"><data value='init__'>GlobalSSHKeyManager.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t180">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t180"><data value='format_ssh_key'>GlobalSSHKeyManager._format_ssh_key</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t257">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t257"><data value='setup_host_key_verification'>GlobalSSHKeyManager.setup_host_key_verification</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t351">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t351"><data value='initialize_ssh_key'>GlobalSSHKeyManager.initialize_ssh_key</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t504">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t504"><data value='get_ssh_key_path'>GlobalSSHKeyManager.get_ssh_key_path</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t523">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t523"><data value='get_ssh_key_path_with_exceptions'>GlobalSSHKeyManager.get_ssh_key_path_with_exceptions</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t543">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t543"><data value='initialize_ssh_key_with_exceptions'>GlobalSSHKeyManager.initialize_ssh_key_with_exceptions</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t555">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t555"><data value='set_ssh_connection_details'>GlobalSSHKeyManager.set_ssh_connection_details</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t567">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t567"><data value='setup_host_key_verification_if_needed'>GlobalSSHKeyManager.setup_host_key_verification_if_needed</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t587">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t587"><data value='cleanup_ssh_key'>GlobalSSHKeyManager.cleanup_ssh_key</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t625">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t625"><data value='del__'>GlobalSSHKeyManager.__del__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t634">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t634"><data value='get_global_ssh_manager'>get_global_ssh_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t647">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t647"><data value='initialize_global_ssh_key'>initialize_global_ssh_key</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t658">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t658"><data value='setup_global_ssh_host_verification'>setup_global_ssh_host_verification</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t670">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t670"><data value='setup_global_ssh_host_verification_async'>setup_global_ssh_host_verification_async</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t685">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html#t685"><data value='cleanup_global_ssh_key'>cleanup_global_ssh_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html">app/services/ssh_manager.py</a></td>
                <td class="name left"><a href="z_c318f3fa19a49f69_ssh_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>3131</td>
                <td>2656</td>
                <td>0</td>
                <td class="right" data-ratio="475 3131">15%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-27 11:01 +0530
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
